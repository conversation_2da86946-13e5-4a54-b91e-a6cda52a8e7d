from decimal import Decimal

import pytest

from costing.dto.xbrl_dto import (
    XBRLAttributeAccessMixin,
    XBRLDataDTO,
    XBRLDirectAccessError,
    XBRLItem,
    XBRLList,
    XBRLListItem,
    XBRLUnitDTO,
)
from costing.models import FinancialStatement


@pytest.fixture
def sample_xbrl_data():
    """Carica i dati XBRL di esempio."""
    return {
        "context": [
            {
                "@id": "IstantEserCorr",
                "entity": {
                    "identifier": {
                        "$": "12345678901",
                        "@scheme": "http://www.infocamere.it",
                    }
                },
                "period": {"instant": "2023-12-31"},
                "scenario": {"itcc-ci:scen": ["itcc-ci:Depositato"]},
            }
        ],
        "unit": [
            {"@id": "Valuta", "measure": ["iso4217:EUR"]},
            {"@id": "shares", "measure": ["shares"]},
            {"@id": "pure", "measure": ["pure"]},
        ],
        "itcc-ci:TotaleDisponibilitaLiquide": {
            "$": 3726054.0,
            "@contextRef": "IstantEserCorr",
            "@decimals": 0,
            "@unitRef": "Valuta",
        },
        "itcc-ci:DatiAnagraficiCodiceFiscale": {
            "$": "12345678901",
            "@contextRef": "IstantEserCorr",
        },
        "itcc-ci:ValoreDecimali": {
            "$": 123.456789,
            "@contextRef": "IstantEserCorr",
            "@decimals": 2,
            "@unitRef": "Valuta",
        },
        "itcc-ci:ValoreNonNumerico": {
            "$": "ABC123",
            "@contextRef": "IstantEserCorr",
        },
        "itcc-ci:VarieAltreRiserve": [
            {
                "itcc-ci:VarieAltreRiserveImporto": [
                    {
                        "$": 4704713.0,
                        "@unitRef": "Valuta",
                        "@decimals": 0,
                        "@contextRef": "IstantEserCorr",
                    }
                ],
                "itcc-ci:VarieAltreRiserveDescrizione": [
                    {
                        "$": "Riserva finanziamento convertendo",
                        "@contextRef": "IstantEserCorr",
                    }
                ],
            },
            {
                "itcc-ci:VarieAltreRiserveDescrizione": [
                    {"@contextRef": "IstantEserCorr"}
                ]
            },
            {
                "itcc-ci:TestAnnidamentoA3Livelli": [
                    {
                        "itcc-ci:ValoreAnnidato": [
                            {
                                "$": "Valore annidato a 3 livelli",
                                "@contextRef": "IstantEserCorr",
                            }
                        ]
                    }
                ]
            },
        ],
    }


def test_xbrl_item_value_converts_integers_to_decimal():
    """Test that XBRLItem.value converts integers to Decimal with numeric unit reference."""  # noqa: E501
    # Sample data with an integer value
    sample_data = {
        "unit": [
            {"@id": "Valuta", "measure": ["iso4217:EUR"]},
        ],
        "itcc-ci:ValoreIntero": {
            "$": 42,  # Integer value
            "@contextRef": "IstantEserCorr",
            "@unitRef": "Valuta",  # Numeric unit reference
        },
    }

    # Create a DTO and access the value
    dto = XBRLDataDTO(sample_data)
    value = dto.ValoreIntero.value

    # Check that the value is a Decimal
    assert isinstance(value, Decimal)
    assert value == Decimal("42")


def test_xbrl_item_value_converts_floats_to_decimal():
    """Test that XBRLItem.value converts floats to Decimal with numeric unit reference."""  # noqa: E501
    # Sample data with a float value
    sample_data = {
        "unit": [
            {"@id": "Valuta", "measure": ["iso4217:EUR"]},
        ],
        "itcc-ci:ValoreFloat": {
            "$": 42.5,  # Float value
            "@contextRef": "IstantEserCorr",
            "@unitRef": "Valuta",  # Numeric unit reference
        },
    }

    # Create a DTO and access the value
    dto = XBRLDataDTO(sample_data)
    value = dto.ValoreFloat.value

    # Check that the value is a Decimal
    assert isinstance(value, Decimal)
    assert value == Decimal("42.5")


def test_xbrl_item_value_does_not_convert_non_numeric_values():
    """Test that XBRLItem.value does not convert non-numeric values."""
    # Sample data with a non-numeric value
    sample_data = {
        "unit": [
            {"@id": "Valuta", "measure": ["iso4217:EUR"]},
        ],
        "itcc-ci:ValoreStringa": {
            "$": "test",  # String value
            "@contextRef": "IstantEserCorr",
        },
    }

    # Create a DTO and access the value
    dto = XBRLDataDTO(sample_data)
    value = dto.ValoreStringa.value

    # Check that the value is still a string
    assert isinstance(value, str)
    assert value == "test"


def test_xbrl_item_value_does_not_convert_values_without_unit_ref():
    """Test that XBRLItem.value does not convert values without a unit reference."""
    # Sample data with a numeric value but no unit reference
    sample_data = {
        "unit": [
            {"@id": "Valuta", "measure": ["iso4217:EUR"]},
        ],
        "itcc-ci:ValoreSenzaUnitRef": {
            "$": 42,  # Integer value
            "@contextRef": "IstantEserCorr",
            # No @unitRef
        },
    }

    # Create a DTO and access the value
    dto = XBRLDataDTO(sample_data)
    value = dto.ValoreSenzaUnitRef.value

    # Check that the value is still an integer
    assert isinstance(value, int)
    assert value == 42


@pytest.mark.django_db
def test_xbrl_data_dto_property(sample_xbrl_data):
    """Testa la property xbrl_data_dto del modello FinancialStatement."""

    fs = FinancialStatement.objects.create(xbrl_data=sample_xbrl_data)
    dto = fs.xbrl_data_dto
    assert dto is not None

    # Verifica l'accesso ai valori
    assert isinstance(dto.TotaleDisponibilitaLiquide.value, Decimal)
    assert dto.TotaleDisponibilitaLiquide.value == Decimal("3726054")

    # Verifica l'accesso all'intero oggetto
    assert isinstance(dto.TotaleDisponibilitaLiquide, XBRLItem)
    assert dto.TotaleDisponibilitaLiquide["$"] == 3726054.0
    assert dto.TotaleDisponibilitaLiquide["@contextRef"] == "IstantEserCorr"

    # Verifica l'accesso ai contesti
    assert len(dto.TotaleDisponibilitaLiquide.context) == 1
    assert dto.TotaleDisponibilitaLiquide.context[0]["@id"] == "IstantEserCorr"

    # Verifica che i valori decimali vengano convertiti in Decimal
    assert isinstance(dto.ValoreDecimali.value, Decimal)
    assert dto.ValoreDecimali.value == Decimal(
        "123.456789"
    )  # Valore originale senza arrotondamento


@pytest.mark.django_db
def test_xbrl_data_dto_with_none_data():
    """Testa il comportamento della property xbrl_data_dto quando xbrl_data è None."""

    fs = FinancialStatement.objects.create()
    assert fs.xbrl_data_dto is None


def test_xbrl_data_dto_direct_usage(sample_xbrl_data):
    """Testa l'utilizzo diretto della classe XBRLDataDTO."""

    # Crea un'istanza di XBRLDataDTO con i dati XBRL di esempio
    dto = XBRLDataDTO(sample_xbrl_data)

    # Verifica l'accesso ai valori
    assert isinstance(dto.TotaleDisponibilitaLiquide.value, Decimal)
    assert dto.TotaleDisponibilitaLiquide.value == Decimal("3726054")

    # Verifica l'accesso all'intero oggetto
    assert isinstance(dto.TotaleDisponibilitaLiquide, XBRLItem)
    assert dto.TotaleDisponibilitaLiquide["$"] == 3726054.0
    assert dto.TotaleDisponibilitaLiquide["@contextRef"] == "IstantEserCorr"

    # Verifica l'accesso ai contesti
    assert len(dto.TotaleDisponibilitaLiquide.context) == 1
    assert dto.TotaleDisponibilitaLiquide.context[0]["@id"] == "IstantEserCorr"

    # Verifica che un attributo inesistente sollevi AttributeError
    with pytest.raises(AttributeError):
        _ = dto.AttributoInesistente

    # Verifica che i valori decimali vengano convertiti in Decimal ma non arrotondati
    assert isinstance(dto.ValoreDecimali.value, Decimal)
    assert dto.ValoreDecimali.value == Decimal(
        "123.456789"
    )  # Valore originale senza arrotondamento

    # Verifica che i valori non numerici rimangano invariati
    assert dto.ValoreNonNumerico.value == "ABC123"
    assert not isinstance(dto.ValoreNonNumerico.value, Decimal)

    # Verifica che i valori senza @unitRef rimangano invariati
    assert dto.DatiAnagraficiCodiceFiscale.value == "12345678901"
    assert not isinstance(dto.DatiAnagraficiCodiceFiscale.value, Decimal)

    # Verifica l'accesso alle unità tramite XBRLUnitDTO
    unit = dto.TotaleDisponibilitaLiquide.get_unit_by_id("Valuta")
    assert unit is not None
    assert unit.id == "Valuta"
    assert unit.measures == ["iso4217:EUR"]
    assert unit.is_numeric is True


def test_xbrl_unit_dto():
    """Testa la classe XBRLUnitDTO."""
    # Test con unità monetaria
    monetary_unit = XBRLUnitDTO({"@id": "Valuta", "measure": ["iso4217:EUR"]})
    assert monetary_unit.id == "Valuta"
    assert monetary_unit.measures == ["iso4217:EUR"]
    assert monetary_unit.is_numeric is True
    assert str(monetary_unit) == "XBRLUnitDTO(id=Valuta, measures=['iso4217:EUR'])"

    # Test con unità non monetaria ma numerica
    numeric_unit = XBRLUnitDTO({"@id": "shares", "measure": ["shares"]})
    assert numeric_unit.id == "shares"
    assert numeric_unit.measures == ["shares"]
    assert numeric_unit.is_numeric is True  # Now shares is considered numeric

    # Test con unità vuota
    empty_unit = XBRLUnitDTO({})
    assert empty_unit.id == ""
    assert empty_unit.measures == []
    assert empty_unit.is_numeric is False

    # Test with non-numeric unit
    non_numeric_unit = XBRLUnitDTO({"@id": "other", "measure": ["other:value"]})
    assert non_numeric_unit.id == "other"
    assert non_numeric_unit.measures == ["other:value"]
    assert non_numeric_unit.is_numeric is False  # Not EUR or shares


def test_xbrl_list_dto(sample_xbrl_data):
    """Test XBRLListDTO class with nested access."""
    # Create XBRLDataDTO instance with sample data
    dto = XBRLDataDTO(sample_xbrl_data)

    # Check that VarieAltreRiserve is an XBRLList instance
    assert isinstance(dto.VarieAltreRiserve, XBRLList)

    # Test attribute access via index and dot notation
    assert isinstance(dto.VarieAltreRiserve[0], XBRLListItem)

    # Verify that nested lists return XBRLList instead of simple values
    assert isinstance(dto.VarieAltreRiserve[0].VarieAltreRiserveDescrizione, XBRLList)
    assert isinstance(dto.VarieAltreRiserve[0].VarieAltreRiserveImporto, XBRLList)

    # Access values through index
    assert (
        dto.VarieAltreRiserve[0].VarieAltreRiserveDescrizione[0]["$"]
        == "Riserva finanziamento convertendo"
    )
    assert dto.VarieAltreRiserve[0].VarieAltreRiserveImporto[0]["$"] == 4704713.0

    # Test direct key access
    assert (
        dto.VarieAltreRiserve[0]["itcc-ci:VarieAltreRiserveDescrizione"][0]["$"]
        == "Riserva finanziamento convertendo"
    )

    # Test index access
    assert len(dto.VarieAltreRiserve) == 3
    assert "itcc-ci:VarieAltreRiserveImporto" in dto.VarieAltreRiserve[0]
    assert "itcc-ci:VarieAltreRiserveDescrizione" in dto.VarieAltreRiserve[0]
    assert "itcc-ci:VarieAltreRiserveDescrizione" in dto.VarieAltreRiserve[1]
    assert "itcc-ci:TestAnnidamentoA3Livelli" in dto.VarieAltreRiserve[2]

    # Test 3-level nested access
    assert isinstance(dto.VarieAltreRiserve[2].TestAnnidamentoA3Livelli, XBRLList)
    assert isinstance(
        dto.VarieAltreRiserve[2].TestAnnidamentoA3Livelli[0].ValoreAnnidato, XBRLList
    )
    assert (
        dto.VarieAltreRiserve[2].TestAnnidamentoA3Livelli[0].ValoreAnnidato[0]["$"]
        == "Valore annidato a 3 livelli"
    )

    # Test iteration
    count = 0
    for item in dto.VarieAltreRiserve:
        assert isinstance(item, XBRLListItem)
        count += 1
    assert count == 3

    # Test that direct attribute access raises XBRLDirectAccessError
    with pytest.raises(XBRLDirectAccessError) as excinfo:
        _ = dto.VarieAltreRiserve.AttributoInesistente

    # Verify error message
    assert "Direct attribute access not allowed" in str(excinfo.value)
    assert "XBRLList[index].attribute_name" in str(excinfo.value)


def test_xbrl_item(sample_xbrl_data):
    """Test XBRLItem class."""
    # Create an XBRLItem instance
    item = XBRLItem(
        sample_xbrl_data["itcc-ci:TotaleDisponibilitaLiquide"], sample_xbrl_data
    )

    # Test basic properties
    assert item.value == Decimal("3726054")  # Now should be Decimal
    assert item.is_numeric is True
    assert item.data == sample_xbrl_data["itcc-ci:TotaleDisponibilitaLiquide"]
    assert item.full_data == sample_xbrl_data

    # Test dictionary-like access
    assert item["$"] == 3726054.0
    assert item["@contextRef"] == "IstantEserCorr"
    assert item["@decimals"] == 0
    assert item["@unitRef"] == "Valuta"

    # Test attribute access using data property
    assert item.data["$"] == 3726054.0
    assert item.data["@contextRef"] == "IstantEserCorr"

    # Test non-existent attribute
    with pytest.raises(AttributeError):
        _ = item.non_existent_attribute


def test_xbrl_list_item(sample_xbrl_data):
    """Test XBRLListItem class."""
    # Create an XBRLListItem instance
    list_item = XBRLListItem(
        sample_xbrl_data["itcc-ci:VarieAltreRiserve"][0], sample_xbrl_data
    )

    # Test basic properties
    assert list_item.data == sample_xbrl_data["itcc-ci:VarieAltreRiserve"][0]
    assert list_item.full_data == sample_xbrl_data

    # Test attribute access with prefixed names
    assert isinstance(list_item.VarieAltreRiserveDescrizione, XBRLList)
    assert isinstance(list_item.VarieAltreRiserveImporto, XBRLList)

    # Test dictionary-like access
    assert "itcc-ci:VarieAltreRiserveDescrizione" in list_item
    assert "itcc-ci:VarieAltreRiserveImporto" in list_item

    # Test non-existent attribute
    with pytest.raises(AttributeError):
        _ = list_item.non_existent_attribute

    # XBRLListItem non ha più la property value
    with pytest.raises(AttributeError):
        _ = list_item.value


def _assert_numeric_value(value, expected_decimal):
    """Helper to assert numeric values in different formats."""
    if isinstance(value, Decimal):
        assert value == expected_decimal
    elif isinstance(value, list) and len(value) == 1:
        item = value[0]
        if isinstance(item, Decimal):
            assert item == expected_decimal
        elif isinstance(item, dict) and "$" in item:
            assert item["$"] == float(expected_decimal)
        else:
            assert item == float(expected_decimal)
    else:
        assert value == float(expected_decimal)


def _assert_string_value(value, expected_string):
    """Helper to assert string values in different formats."""
    if isinstance(value, str):
        assert value == expected_string
    elif isinstance(value, list) and len(value) == 1:
        item = value[0]
        if isinstance(item, str):
            assert item == expected_string
        elif isinstance(item, dict) and "$" in item:
            assert item["$"] == expected_string
        else:
            assert item == expected_string
    else:
        assert value == expected_string


def _test_basic_structure(items):
    """Test the basic structure of the items dictionary."""
    # Test that it's a dictionary
    assert isinstance(items, dict)

    # Test that internal XBRL structures are excluded
    assert "context" not in items
    assert "unit" not in items

    # Test that keys without the prefix are included
    assert "TotaleDisponibilitaLiquide" in items
    assert "DatiAnagraficiCodiceFiscale" in items
    assert "ValoreDecimali" in items
    assert "ValoreNonNumerico" in items
    assert "VarieAltreRiserve" in items


def _test_simple_values(items):
    """Test simple value extraction from $ field."""
    # Test numeric values are converted to Decimal
    assert isinstance(items["TotaleDisponibilitaLiquide"], Decimal)
    assert items["TotaleDisponibilitaLiquide"] == Decimal("3726054")
    assert isinstance(items["ValoreDecimali"], Decimal)
    assert items["ValoreDecimali"] == Decimal("123.456789")

    # Test non-numeric values remain unchanged
    assert items["ValoreNonNumerico"] == "ABC123"
    assert items["DatiAnagraficiCodiceFiscale"] == "12345678901"


def _test_nested_structure(items, sample_xbrl_data):
    """Test nested structure with lists."""
    # Test lists are processed correctly
    assert isinstance(items["VarieAltreRiserve"], list)
    assert len(items["VarieAltreRiserve"]) == 3

    # Test first item (with nested values)
    first_item = items["VarieAltreRiserve"][0]
    assert isinstance(first_item, dict)
    assert "VarieAltreRiserveImporto" in first_item
    assert "VarieAltreRiserveDescrizione" in first_item

    # Test values are extracted correctly
    _assert_numeric_value(first_item["VarieAltreRiserveImporto"], Decimal("4704713"))
    _assert_string_value(
        first_item["VarieAltreRiserveDescrizione"], "Riserva finanziamento convertendo"
    )

    # Test second item (with missing value)
    second_item = items["VarieAltreRiserve"][1]
    assert isinstance(second_item, dict)
    assert "VarieAltreRiserveDescrizione" in second_item

    # Test value might be a list without $ field or an extracted value
    if isinstance(second_item["VarieAltreRiserveDescrizione"], list):
        assert len(second_item["VarieAltreRiserveDescrizione"]) == 1
        assert "$" not in second_item["VarieAltreRiserveDescrizione"][0]
    else:
        # Might be None or a value extracted from another field (like @contextRef)
        assert second_item["VarieAltreRiserveDescrizione"] is None or isinstance(
            second_item["VarieAltreRiserveDescrizione"], str
        )

    # Test third item (deeply nested structure - 3 levels)
    third_item = items["VarieAltreRiserve"][2]
    assert "TestAnnidamentoA3Livelli" in third_item

    # Test value might be a list or directly extracted value
    if isinstance(third_item["TestAnnidamentoA3Livelli"], list):
        assert len(third_item["TestAnnidamentoA3Livelli"]) == 1
        nested_item = third_item["TestAnnidamentoA3Livelli"][0]
        assert "ValoreAnnidato" in nested_item

        # Test value might be a string or nested object
        if isinstance(nested_item["ValoreAnnidato"], str):
            assert nested_item["ValoreAnnidato"] == "Valore annidato a 3 livelli"
        else:
            assert (
                isinstance(nested_item["ValoreAnnidato"], list)
                or nested_item["ValoreAnnidato"] is None
            )
    else:
        # If implementation directly extracts the deepest value
        # Verify the value is present in the original data
        original_value = sample_xbrl_data["itcc-ci:VarieAltreRiserve"][2][
            "itcc-ci:TestAnnidamentoA3Livelli"
        ][0]["itcc-ci:ValoreAnnidato"][0]["$"]
        assert original_value == "Valore annidato a 3 livelli"


def _test_specific_example(specific_data):
    """Test with specific data to verify exact behavior as requested."""
    specific_dto = XBRLDataDTO(specific_data)
    specific_items = specific_dto.items

    # Test TotaleDisponibilitaLiquide
    assert "TotaleDisponibilitaLiquide" in specific_items
    assert specific_items["TotaleDisponibilitaLiquide"] == Decimal("3726054")

    # Test VarieAltreRiserve
    assert "VarieAltreRiserve" in specific_items

    # Test implementation might return a list or directly a dictionary
    if isinstance(specific_items["VarieAltreRiserve"], list):
        assert len(specific_items["VarieAltreRiserve"]) == 1
        specific_first_item = specific_items["VarieAltreRiserve"][0]

        # Test fields are present
        assert "VarieAltreRiserveImporto" in specific_first_item
        assert "VarieAltreRiserveDescrizione" in specific_first_item

        # Test values
        _assert_numeric_value(
            specific_first_item["VarieAltreRiserveImporto"], Decimal("4704713")
        )
        _assert_string_value(
            specific_first_item["VarieAltreRiserveDescrizione"], "Riserva finanziamento"
        )
    else:
        # Verify extracted values match original data
        original_importo = specific_data["itcc-ci:VarieAltreRiserve"][0][
            "itcc-ci:VarieAltreRiserveImporto"
        ][0]["$"]
        original_descrizione = specific_data["itcc-ci:VarieAltreRiserve"][0][
            "itcc-ci:VarieAltreRiserveDescrizione"
        ][0]["$"]
        assert original_importo == 4704713.0
        assert original_descrizione == "Riserva finanziamento"


def test_xbrl_data_dto_items_property(sample_xbrl_data):
    """Test the items property of XBRLDataDTO."""
    # Create an XBRLDataDTO instance
    dto = XBRLDataDTO(sample_xbrl_data)
    items = dto.items

    # Run tests on different aspects of the items property
    _test_basic_structure(items)
    _test_simple_values(items)
    _test_nested_structure(items, sample_xbrl_data)

    # Test with specific example data
    specific_data = {
        "unit": [{"@id": "Valuta", "measure": ["iso4217:EUR"]}],
        "itcc-ci:TotaleDisponibilitaLiquide": {
            "$": 3726054.0,
            "@contextRef": "IstantEserCorr",
            "@decimals": 0,
            "@unitRef": "Valuta",
        },
        "itcc-ci:VarieAltreRiserve": [
            {
                "itcc-ci:VarieAltreRiserveImporto": [
                    {
                        "$": 4704713.0,
                        "@unitRef": "Valuta",
                        "@decimals": 0,
                        "@contextRef": "IstantEserCorr",
                    }
                ],
                "itcc-ci:VarieAltreRiserveDescrizione": [
                    {
                        "$": "Riserva finanziamento",
                        "@contextRef": "IstantEserCorr",
                    }
                ],
            }
        ],
    }
    _test_specific_example(specific_data)


def test_xbrl_list_item_unexpected_data_structure():
    """Test that XBRLListItem raises ValueError for unexpected data structures."""
    # Create invalid XBRL data with a prefixed attribute that is neither
    # an XBRL item (dict with $ key) nor an XBRL list (list of dicts)
    invalid_data = {
        "itcc-ci:UnexpectedValue": "simple_string",  # This should never happen
        "other_key": "normal_value",
    }

    full_data = {"unit": [{"@id": "Valuta", "measure": ["iso4217:EUR"]}], "context": []}

    # Create XBRLListItem with invalid data
    list_item = XBRLListItem(invalid_data, full_data)

    # Accessing the unexpected attribute should raise ValueError
    with pytest.raises(ValueError, match="Unexpected XBRL data structure") as excinfo:
        _ = list_item.UnexpectedValue

    # Verify error message
    assert "Unexpected XBRL data structure for attribute 'UnexpectedValue'" in str(
        excinfo.value
    )
    assert "Expected XBRL item (dict with '$' key) or XBRL list" in str(excinfo.value)
    assert "but got: str" in str(excinfo.value)


def test_xbrl_data_dto_unexpected_data_structure():
    """Test that XBRLDataDTO raises ValueError for unexpected data structures."""
    # Create invalid XBRL data with a prefixed attribute that is neither
    # an XBRL item (dict with $ key) nor an XBRL list (list of dicts)
    invalid_data = {
        "unit": [{"@id": "Valuta", "measure": ["iso4217:EUR"]}],
        "context": [],
        "itcc-ci:UnexpectedValue": 42,  # This should never happen - numeric value
        "itcc-ci:AnotherUnexpectedValue": True,  # This should never happen - boolean
    }

    # Create XBRLDataDTO with invalid data
    dto = XBRLDataDTO(invalid_data)

    # Accessing the unexpected numeric attribute should raise ValueError
    with pytest.raises(ValueError, match="Unexpected XBRL data structure") as excinfo:
        _ = dto.UnexpectedValue

    # Verify error message for numeric value
    assert "Unexpected XBRL data structure for attribute 'UnexpectedValue'" in str(
        excinfo.value
    )
    assert "Expected XBRL item (dict with '$' key) or XBRL list" in str(excinfo.value)
    assert "but got: int" in str(excinfo.value)

    # Accessing the unexpected boolean attribute should also raise ValueError
    with pytest.raises(ValueError, match="Unexpected XBRL data structure") as excinfo:
        _ = dto.AnotherUnexpectedValue

    # Verify error message for boolean value
    assert (
        "Unexpected XBRL data structure for attribute 'AnotherUnexpectedValue'"
        in str(excinfo.value)
    )
    assert "Expected XBRL item (dict with '$' key) or XBRL list" in str(excinfo.value)
    assert "but got: bool" in str(excinfo.value)


def test_xbrl_attribute_access_mixin_inheritance():
    """Test that XBRLListItem and XBRLDataDTO properly inherit from XBRLAttributeAccessMixin."""  # noqa: E501
    # Test that both classes inherit from the mixin
    assert issubclass(XBRLListItem, XBRLAttributeAccessMixin)
    assert issubclass(XBRLDataDTO, XBRLAttributeAccessMixin)

    # Test that the mixin's __getattr__ method is used
    assert hasattr(XBRLListItem, "__getattr__")
    assert hasattr(XBRLDataDTO, "__getattr__")

    # Verify that both classes implement get_full_data
    assert hasattr(XBRLListItem, "get_full_data")
    assert hasattr(XBRLDataDTO, "get_full_data")


def test_xbrl_attribute_access_mixin_functionality(sample_xbrl_data):
    """Test that the mixin provides the same functionality as before refactoring."""
    # Test XBRLDataDTO functionality
    dto = XBRLDataDTO(sample_xbrl_data)

    # Test prefixed attribute access
    assert isinstance(dto.TotaleDisponibilitaLiquide, XBRLItem)
    assert isinstance(dto.VarieAltreRiserve, XBRLList)

    # Test direct attribute access
    assert dto.context == sample_xbrl_data["context"]
    assert dto.unit == sample_xbrl_data["unit"]

    # Test XBRLListItem functionality
    list_item = XBRLListItem(
        sample_xbrl_data["itcc-ci:VarieAltreRiserve"][0], sample_xbrl_data
    )

    # Test prefixed attribute access
    assert isinstance(list_item.VarieAltreRiserveDescrizione, XBRLList)
    assert isinstance(list_item.VarieAltreRiserveImporto, XBRLList)

    # Test that both classes raise AttributeError for non-existent attributes
    with pytest.raises(AttributeError):
        _ = dto.NonExistentAttribute

    with pytest.raises(AttributeError):
        _ = list_item.NonExistentAttribute
