import html
import json

from django.contrib import admin, messages
from django.http import HttpResponseRedirect
from django.urls import path, reverse
from django.utils.safestring import mark_safe

from common.admin import BaseModelAdmin, PrettyJSONMixin
from costing.models import FinancialStatement


@admin.register(FinancialStatement)
class FinancialStatementAdmin(PrettyJSONMixin, BaseModelAdmin):
    def get_fieldsets(self, request, obj=None):
        fieldsets = [
            (None, {"fields": ["pdf_file", "xbrl_file", "csv_file"]}),
            ("Extracted Data", {"fields": ["xbrl_data", "csv_data"]}),
        ]

        if obj and obj.xbrl_data:
            fieldsets.append(
                (
                    "xbrl_date_dto.items",
                    {
                        "fields": [],
                        "classes": ("collapse",),
                        "description": self._get_data_html(obj.xbrl_data_dto.items),
                    },
                )
            )

        return fieldsets

    def _get_data_html(self, data):
        """Generates HTML to display xbrl_data_dto.items."""
        try:
            formatted_json = json.dumps(data, indent=2, default=str)
            escaped_json = html.escape(formatted_json)

            # Return the formatted HTML
            return mark_safe(  # noqa: S308
                '<pre style="max-height: 500px; overflow: auto; '
                "background-color: #f5f5f5; padding: 10px; "
                'border: 1px solid #ddd; border-radius: 4px;">'
                f"{escaped_json}"
                "</pre>"
            )
        except (TypeError, ValueError, KeyError, json.JSONDecodeError) as e:
            return mark_safe(  # noqa: S308
                f"<p>Error processing XBRL data: {e!s}</p>"
            )

    def get_urls(self):
        urls = super().get_urls()
        extra_urls = [
            path(
                "<int:pk>/parse_xbrl/",
                self.admin_site.admin_view(self.parse_xbrl),
                name="parse_xbrl",
            ),
            path(
                "<int:pk>/parse_csv/",
                self.admin_site.admin_view(self.parse_csv),
                name="parse_csv",
            ),
        ]
        return extra_urls + urls

    def parse_xbrl(self, request, pk):
        fs = self.get_object(request, pk)
        try:
            fs.parse_xbrl()
        except Exception as e:  # noqa: BLE001
            messages.error(request, str(e))
        else:
            messages.success(request, "XBRL file processed successfully.")
        return HttpResponseRedirect(
            reverse(
                "admin:costing_financialstatement_change",
                kwargs={"object_id": fs.id},
            )
        )

    def parse_csv(self, request, pk):
        fs = self.get_object(request, pk)
        try:
            fs.parse_csv()
        except Exception as e:  # noqa: BLE001
            messages.error(request, str(e))
        else:
            messages.success(request, "CSV file processed successfully.")
        return HttpResponseRedirect(
            reverse(
                "admin:costing_financialstatement_change",
                kwargs={"object_id": fs.id},
            )
        )

    def change_view(self, request, object_id, form_url="", extra_context=None):
        if request.method == "POST":
            if "_parse_xbrl" in request.POST:
                self.parse_xbrl(request=request, pk=object_id)
                return HttpResponseRedirect(request.path)
            if "_parse_csv" in request.POST:
                self.parse_csv(request=request, pk=object_id)
                return HttpResponseRedirect(request.path)
        return super().change_view(request, object_id, form_url, extra_context)
