services:

  costing_db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: costing
      POSTGRES_PASSWORD: costing
      POSTGRES_DB: costing
    expose:
      - 5432
    volumes:
      - ../shared:/shared
      - costing_postgres_data:/var/lib/postgresql/data/

  costing_backend:
    container_name: costing_backend
    build:
      context: ../..
      dockerfile: docker/dev/costing_backend.Dockerfile
    image: costing_backend
    stdin_open: true
    tty: true
    volumes:
      - ../..:/usr/src/costing/
      - ipython_history:/root/.ipython
      - costing_media:/usr/src/costing/backend/django/project/media/
      - costing_venv:/opt/venv
    ports:
      - 3001:3000
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - costing_db
    command: >
      sh -c 'python -Wa manage.py runserver 0.0.0.0:8000;'

  costing_frontend:
    build:
      context: ../..
      dockerfile: docker/dev/costing_frontend.Dockerfile
    image: costing_frontend
    volumes:
      - ../..:/usr/src/costing/
      - ../../frontend/node_modules:/usr/src/costing/frontend/node_modules
    entrypoint: /usr/src/costing/docker/dev/costing_frontend-entrypoint.sh

  costing_backoffice:
    image: costing_frontend
    expose:
      - 5173
    volumes:
      - ../..:/usr/src/costing/
      - ../../frontend/node_modules:/usr/src/costing/frontend/node_modules
    depends_on:
      costing_frontend:
        condition: service_completed_successfully
    command: npm run backoffice:dev

  costing_horizon:
    image: costing_frontend
    expose:
      - 5174
    volumes:
      - ../..:/usr/src/costing/
      - ../../frontend/node_modules:/usr/src/costing/frontend/node_modules
    depends_on:
      costing_frontend:
        condition: service_completed_successfully
    command: npm run horizon:dev

  costing_ws:
    build:
      context: ../..
      dockerfile: docker/dev/costing_ws.Dockerfile
    ports:
      - 80:8080
    depends_on:
      - costing_backend
      - costing_backoffice
      - costing_horizon

volumes:
  ipython_history:
  costing_media:
  costing_postgres_data:
  costing_venv:
