FROM --platform=linux/amd64 python:3.12-alpine3.20
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONBREAKPOINT ipdb.set_trace
ENV COSTING_ENV dev
RUN apk update
RUN apk add --no-cache git openssh-client
RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN git config --global --add safe.directory /usr/src/costing
WORKDIR /usr/src/costing/backend/django
RUN python -m ensurepip --upgrade
ENTRYPOINT ["/usr/src/costing/docker/dev/costing_backend-entrypoint.sh"]