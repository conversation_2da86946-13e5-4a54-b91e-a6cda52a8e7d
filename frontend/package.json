{"name": "costing_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"backoffice:dev": "VITE_SPA=backoffice VITE_ENV=dev vite --mode dev", "horizon:dev": "VITE_SPA=horizon-app VITE_ENV=dev vite --mode dev --port 5174", "format": "prettier . --check", "format:fix": "prettier . --write"}, "dependencies": {"@primevue/forms": "^4.3.1", "@primevue/themes": "^4.3.1", "@tailwindcss/vite": "^4.0.9", "@tanstack/vue-query": "^5.66.9", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "primevue": "^4.3.1", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.9", "tiny-emitter": "^2.1.0", "vue": "~3.5.13", "vue-router": "~4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@iconify/vue": "^4.3.0", "@vitejs/plugin-vue": "^5.2.1", "eslint": "^9.21.0", "eslint-plugin-jethr": "^1.0.3", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.5.2", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.85.1", "stylelint": "^16.14.1", "stylelint-config-standard": "^37.0.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-config-standard-vue": "^1.0.0", "unplugin-auto-import": "^19.1.1", "vite": "^6.2.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-musl": "4.34.8"}}